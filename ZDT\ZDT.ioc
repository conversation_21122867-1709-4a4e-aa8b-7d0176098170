#MicroXplorer Configuration settings - do not modify
CAD.formats=
CAD.pinconfig=
CAD.provider=
Dma.Request0=USART2_RX
Dma.Request1=UART4_RX
Dma.Request2=USART6_RX
Dma.Request3=USART3_RX
Dma.RequestsNb=4
Dma.UART4_RX.1.Direction=DMA_PERIPH_TO_MEMORY
Dma.UART4_RX.1.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.UART4_RX.1.Instance=DMA1_Stream2
Dma.UART4_RX.1.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.UART4_RX.1.MemInc=DMA_MINC_ENABLE
Dma.UART4_RX.1.Mode=DMA_NORMAL
Dma.UART4_RX.1.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.UART4_RX.1.PeriphInc=DMA_PINC_DISABLE
Dma.UART4_RX.1.Priority=DMA_PRIORITY_LOW
Dma.UART4_RX.1.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.USART2_RX.0.Direction=DMA_PERIPH_TO_MEMORY
Dma.USART2_RX.0.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART2_RX.0.Instance=DMA1_Stream5
Dma.USART2_RX.0.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART2_RX.0.MemInc=DMA_MINC_ENABLE
Dma.USART2_RX.0.Mode=DMA_NORMAL
Dma.USART2_RX.0.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART2_RX.0.PeriphInc=DMA_PINC_DISABLE
Dma.USART2_RX.0.Priority=DMA_PRIORITY_LOW
Dma.USART2_RX.0.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.USART3_RX.3.Direction=DMA_PERIPH_TO_MEMORY
Dma.USART3_RX.3.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART3_RX.3.Instance=DMA1_Stream1
Dma.USART3_RX.3.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART3_RX.3.MemInc=DMA_MINC_ENABLE
Dma.USART3_RX.3.Mode=DMA_NORMAL
Dma.USART3_RX.3.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART3_RX.3.PeriphInc=DMA_PINC_DISABLE
Dma.USART3_RX.3.Priority=DMA_PRIORITY_LOW
Dma.USART3_RX.3.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.USART6_RX.2.Direction=DMA_PERIPH_TO_MEMORY
Dma.USART6_RX.2.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART6_RX.2.Instance=DMA2_Stream1
Dma.USART6_RX.2.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART6_RX.2.MemInc=DMA_MINC_ENABLE
Dma.USART6_RX.2.Mode=DMA_NORMAL
Dma.USART6_RX.2.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART6_RX.2.PeriphInc=DMA_PINC_DISABLE
Dma.USART6_RX.2.Priority=DMA_PRIORITY_LOW
Dma.USART6_RX.2.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
File.Version=6
GPIO.groupedBy=
KeepUserPlacement=false
Mcu.CPN=STM32F407VET6
Mcu.Family=STM32F4
Mcu.IP0=DMA
Mcu.IP1=NVIC
Mcu.IP10=USART2
Mcu.IP11=USART3
Mcu.IP12=USART6
Mcu.IP2=RCC
Mcu.IP3=SYS
Mcu.IP4=TIM1
Mcu.IP5=TIM3
Mcu.IP6=TIM4
Mcu.IP7=UART4
Mcu.IP8=UART5
Mcu.IP9=USART1
Mcu.IPNb=13
Mcu.Name=STM32F407V(E-G)Tx
Mcu.Package=LQFP100
Mcu.Pin0=PE3
Mcu.Pin1=PE4
Mcu.Pin10=PE11
Mcu.Pin11=PE14
Mcu.Pin12=PB10
Mcu.Pin13=PB11
Mcu.Pin14=PD12
Mcu.Pin15=PD13
Mcu.Pin16=PC6
Mcu.Pin17=PC7
Mcu.Pin18=PA9
Mcu.Pin19=PA10
Mcu.Pin2=PC14-OSC32_IN
Mcu.Pin20=PA13
Mcu.Pin21=PA14
Mcu.Pin22=PC12
Mcu.Pin23=PD2
Mcu.Pin24=PB4
Mcu.Pin25=PB5
Mcu.Pin26=VP_SYS_VS_Systick
Mcu.Pin27=VP_TIM1_VS_ClockSourceINT
Mcu.Pin3=PC15-OSC32_OUT
Mcu.Pin4=PH0-OSC_IN
Mcu.Pin5=PH1-OSC_OUT
Mcu.Pin6=PA0-WKUP
Mcu.Pin7=PA1
Mcu.Pin8=PA2
Mcu.Pin9=PA3
Mcu.PinsNb=28
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32F407VETx
MxCube.Version=6.14.1
MxDb.Version=DB.6.0.141
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.DMA1_Stream1_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DMA1_Stream2_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DMA1_Stream5_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DMA2_Stream1_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.SysTick_IRQn=true\:15\:0\:false\:false\:true\:false\:true\:false
NVIC.UART4_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.USART1_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.USART2_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.USART3_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.USART6_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
PA0-WKUP.Locked=true
PA0-WKUP.Mode=Asynchronous
PA0-WKUP.Signal=UART4_TX
PA1.Locked=true
PA1.Mode=Asynchronous
PA1.Signal=UART4_RX
PA10.Mode=Asynchronous
PA10.Signal=USART1_RX
PA13.Mode=Serial_Wire
PA13.Signal=SYS_JTMS-SWDIO
PA14.Mode=Serial_Wire
PA14.Signal=SYS_JTCK-SWCLK
PA2.Mode=Asynchronous
PA2.Signal=USART2_TX
PA3.Mode=Asynchronous
PA3.Signal=USART2_RX
PA9.Mode=Asynchronous
PA9.Signal=USART1_TX
PB10.Mode=Asynchronous
PB10.Signal=USART3_TX
PB11.Mode=Asynchronous
PB11.Signal=USART3_RX
PB4.Locked=true
PB4.Signal=S_TIM3_CH1
PB5.Locked=true
PB5.Signal=S_TIM3_CH2
PC12.Mode=Asynchronous
PC12.Signal=UART5_TX
PC14-OSC32_IN.Mode=LSE-External-Oscillator
PC14-OSC32_IN.Signal=RCC_OSC32_IN
PC15-OSC32_OUT.Mode=LSE-External-Oscillator
PC15-OSC32_OUT.Signal=RCC_OSC32_OUT
PC6.Mode=Asynchronous
PC6.Signal=USART6_TX
PC7.Mode=Asynchronous
PC7.Signal=USART6_RX
PD12.Locked=true
PD12.Signal=S_TIM4_CH1
PD13.Locked=true
PD13.Signal=S_TIM4_CH2
PD2.Mode=Asynchronous
PD2.Signal=UART5_RX
PE11.Locked=true
PE11.Signal=S_TIM1_CH2
PE14.Locked=true
PE14.Signal=S_TIM1_CH4
PE3.GPIOParameters=GPIO_PuPd
PE3.GPIO_PuPd=GPIO_PULLUP
PE3.Locked=true
PE3.Signal=GPIO_Input
PE4.GPIOParameters=GPIO_PuPd
PE4.GPIO_PuPd=GPIO_PULLUP
PE4.Locked=true
PE4.Signal=GPIO_Input
PH0-OSC_IN.Mode=HSE-External-Oscillator
PH0-OSC_IN.Signal=RCC_OSC_IN
PH1-OSC_OUT.Mode=HSE-External-Oscillator
PH1-OSC_OUT.Signal=RCC_OSC_OUT
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerLinker=GCC
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32F407VETx
ProjectManager.FirmwarePackage=STM32Cube FW_F4 V1.28.2
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=0
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=ZDT.ioc
ProjectManager.ProjectName=ZDT
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=MDK-ARM V5.32
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_DMA_Init-DMA-false-HAL-true,4-MX_USART2_UART_Init-USART2-false-HAL-true,5-MX_UART4_Init-UART4-false-HAL-true,6-MX_TIM1_Init-TIM1-false-HAL-true,7-MX_TIM3_Init-TIM3-false-HAL-true,8-MX_TIM4_Init-TIM4-false-HAL-true,9-MX_UART5_Init-UART5-false-HAL-true,10-MX_USART3_UART_Init-USART3-false-HAL-true,11-MX_USART6_UART_Init-USART6-false-HAL-true,12-MX_USART1_UART_Init-USART1-false-HAL-true
RCC.AHBFreq_Value=16000000
RCC.APB1CLKDivider=RCC_HCLK_DIV2
RCC.APB1Freq_Value=8000000
RCC.APB1TimFreq_Value=16000000
RCC.APB2Freq_Value=16000000
RCC.CortexFreq_Value=16000000
RCC.FamilyName=M
RCC.HSE_VALUE=25000000
RCC.HSI_VALUE=16000000
RCC.I2SClocksFreq_Value=96000000
RCC.IPParameters=AHBFreq_Value,APB1CLKDivider,APB1Freq_Value,APB1TimFreq_Value,APB2Freq_Value,CortexFreq_Value,FamilyName,HSE_VALUE,HSI_VALUE,I2SClocksFreq_Value,LSI_VALUE,PLLCLKFreq_Value,PLLQCLKFreq_Value,RTCFreq_Value,RTCHSEDivFreq_Value,SYSCLKFreq_VALUE,VCOI2SOutputFreq_Value,VCOInputFreq_Value,VCOOutputFreq_Value,VcooutputI2S
RCC.LSI_VALUE=32000
RCC.PLLCLKFreq_Value=96000000
RCC.PLLQCLKFreq_Value=48000000
RCC.RTCFreq_Value=32000
RCC.RTCHSEDivFreq_Value=12500000
RCC.SYSCLKFreq_VALUE=16000000
RCC.VCOI2SOutputFreq_Value=192000000
RCC.VCOInputFreq_Value=1000000
RCC.VCOOutputFreq_Value=192000000
RCC.VcooutputI2S=96000000
SH.S_TIM1_CH2.0=TIM1_CH2,PWM Generation2 CH2
SH.S_TIM1_CH2.ConfNb=1
SH.S_TIM1_CH4.0=TIM1_CH4,PWM Generation4 CH4
SH.S_TIM1_CH4.ConfNb=1
SH.S_TIM3_CH1.0=TIM3_CH1,Encoder_Interface
SH.S_TIM3_CH1.ConfNb=1
SH.S_TIM3_CH2.0=TIM3_CH2,Encoder_Interface
SH.S_TIM3_CH2.ConfNb=1
SH.S_TIM4_CH1.0=TIM4_CH1,Encoder_Interface
SH.S_TIM4_CH1.ConfNb=1
SH.S_TIM4_CH2.0=TIM4_CH2,Encoder_Interface
SH.S_TIM4_CH2.ConfNb=1
TIM1.Channel-PWM\ Generation2\ CH2=TIM_CHANNEL_2
TIM1.Channel-PWM\ Generation4\ CH4=TIM_CHANNEL_4
TIM1.IPParameters=Channel-PWM Generation2 CH2,Channel-PWM Generation4 CH4,Prescaler
TIM1.Prescaler=40-1
TIM3.EncoderMode=TIM_ENCODERMODE_TI12
TIM3.IPParameters=EncoderMode
TIM4.EncoderMode=TIM_ENCODERMODE_TI12
TIM4.IPParameters=EncoderMode
UART4.IPParameters=VirtualMode
UART4.VirtualMode=Asynchronous
UART5.IPParameters=VirtualMode
UART5.VirtualMode=Asynchronous
USART1.IPParameters=VirtualMode
USART1.VirtualMode=VM_ASYNC
USART2.IPParameters=VirtualMode
USART2.VirtualMode=VM_ASYNC
USART3.IPParameters=VirtualMode
USART3.VirtualMode=VM_ASYNC
USART6.IPParameters=VirtualMode
USART6.VirtualMode=VM_ASYNC
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
VP_TIM1_VS_ClockSourceINT.Mode=Internal
VP_TIM1_VS_ClockSourceINT.Signal=TIM1_VS_ClockSourceINT
board=custom
